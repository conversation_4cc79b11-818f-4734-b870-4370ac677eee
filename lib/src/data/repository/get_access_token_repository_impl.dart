import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:http/browser_client.dart';
import 'package:http/http.dart' as http;
import 'package:neorevv/src/core/network/dio_refresh.dart';
import 'package:neorevv/src/data/repository/auth_data_repository.dart';

import '../../core/config/app_strings.dart';
import '../../core/network/api_config.dart';
import '../../core/network/dio_client.dart';
import '../../core/services/api_error_handler.dart';
import '/src/core/services/exceptions.dart';
import '/src/core/services/flutter_secure_storage.dart';
import 'package:dio/dio.dart';
import '/src/domain/models/login.dart';
import '../../domain/repository/get_access_token_repository.dart';

class GetAccessTokenRepositoryImpl extends GetAccessTokenRepository {
  GetAccessTokenRepositoryImpl();

  static const String baseUrl = APIConfig.baseUrl;
  static const String refreshTokenUrl = APIConfig.refreshtoken;

  @override
  // Future<LoginModel?> getAccessToken(String refreshToken) async {
  //   try {
  //     final dio = await DioClient.getDio();
  //     final response = await dio.post(
  //       refreshTokenUrl,
  //       data: {'refreshToken': refreshToken},
  //     );
  //     if (response.statusCode == 200) {
  //       final sessionManager = SessionManager();
  //       await sessionManager.saveSession(LoginModel.fromJson(response.data));
  //       return LoginModel.fromJson(response.data);
  //     } else {
  //       return null;
  //     }
  //   } on DioException catch (e) {
  //     throw ApiErrorHandler.handleDioException(e, failedToRefreshToken);
  //   } catch (e) {
  //     throw ApiException(message: e.toString(), statusCode: 500);
  //   }
  // }
  Future<LoginModel?> getAccessToken(String refreshToken) async {
    try {
      // Create Dio instance (cookie-friendly for web)
      final dio = DioRefreshTokenClient.createInstance();
      // No body — server will read refresh cookie
      final response = await dio.post(refreshTokenUrl);
      if (response.statusCode == 200) {
        final loginModel = LoginModel.fromJson(response.data);
        // Store updated session

        // final sessionManager = SessionManager();
        // await sessionManager.saveSession(loginModel);

        final authRepository = AuthDataRepository();
        authRepository.setTokens(loginModel.jwt, loginModel.refreshToken);

        return loginModel;
      } else {
        return null;
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, "Failed to refresh token");
    } catch (e) {
      throw ApiException(message: e.toString(), statusCode: 500);
    }
  }
  // Future<LoginModel?> getAccessToken(String refreshToken) async {
  //   try {
  //     // Create HTTP client
  //     final client = http.Client();
  //     final uri = Uri.parse(refreshTokenUrl);
  //     // Prepare headers
  //     final headers = {
  //       'Content-Type': 'application/json',
  //       if (kIsWeb) 'withCredentials': 'true', // Ensure cookies are sent on web
  //     };
  //     // Make POST request
  //     final response = await client
  //         .post(uri, headers: headers)
  //         .timeout(const Duration(seconds: 10));
  //     if (response.statusCode == 200) {
  //       final loginModel = LoginModel.fromJson(jsonDecode(response.body));
  //       // Store updated session
  //       final sessionManager = SessionManager();
  //       await sessionManager.saveSession(loginModel);
  //       return loginModel;
  //     } else {
  //       return null;
  //     }
  //   } on http.ClientException catch (e) {
  //     throw ApiException(
  //       message: 'Failed to refresh token: $e',
  //       statusCode: 500,
  //     );
  //   } on TimeoutException catch (e) {
  //     throw ApiException(message: 'Request timed out: $e', statusCode: 408);
  //   } catch (e) {
  //     throw ApiException(message: e.toString(), statusCode: 500);
  //   }
  // }
  // Assume LoginModel, ApiException, SessionManager are defined elsewhere
  // Future<LoginModel?> getAccessToken(String refreshToken) async {
  //   // Use http.Client for mobile and a configured BrowserClient for web
  //   final http.Client client;

  //   if (kIsWeb) {
  //     // 2. Create a BrowserClient and enable credentials
  //     client = BrowserClient()..withCredentials = true;
  //   } else {
  //     // Use a standard client for non-web platforms
  //     client = http.Client();
  //   }

  //   try {
  //     final uri = Uri.parse(baseUrl + refreshTokenUrl); // Replace with your URL

  //     // 3. The 'withCredentials' key is REMOVED from the headers
  //     final headers = {'Content-Type': 'application/json'};

  //     // Make POST request using the configured client
  //     final response = await client
  //         .post(uri, headers: headers)
  //         .timeout(const Duration(seconds: 10));

  //     if (response.statusCode == 200) {
  //       final loginModel = LoginModel.fromJson(jsonDecode(response.body));
  //       final sessionManager = SessionManager();
  //       await sessionManager.saveSession(loginModel);
  //       return loginModel;
  //     } else {
  //       return null;
  //     }
  //   } on http.ClientException catch (e) {
  //     throw ApiException(
  //       message: 'Failed to refresh token: $e',
  //       statusCode: 500,
  //     );
  //   } on TimeoutException catch (e) {
  //     throw ApiException(message: 'Request timed out: $e', statusCode: 408);
  //   } catch (e) {
  //     throw ApiException(message: e.toString(), statusCode: 500);
  //   } finally {
  //     // Don't forget to close the client to free up resources
  //     client.close();
  //   }
  // }
}
