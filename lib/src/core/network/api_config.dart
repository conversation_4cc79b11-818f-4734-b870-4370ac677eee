class APIConfig {
  const APIConfig._();

  //static const baseUrl = 'http://neorevv.local:8080';
  static const baseUrl = 'http://10.10.11.191:8080';

  static const login = '/v1/auth/token';
  static const userProfile = '/v1/user/profile';
  static const infocard = '/v1/dashboard/statistics';
  static const refreshtoken = '/v1/auth/token/refresh';
  static const brokers = '/v1/broker/search';
  static const brokerageTopPerformers =
      '/v1/dashboard/brokerage/top-performers';
  static const agentTopPerformers = '/v1/dashboard/agent/top-performers';

  //DASHBOARD
  static const totalPropertyValue = '/v1/dashboard/property-value';
  static const totalSales = '/v1/dashboard/sales';
  static const totalRevenue = '/v1/dashboard/revenue';
  static const totalCommissions = '/v1/dashboard/commissions';
  static const totalBrokerages = '/v1/dashboard/brokerages';
  static const totalAgents = '/v1/dashboard/agents';

  //AGENT NETWORK
  static const agentNetwork = '/v1/hierarchy-details';
  static const agentsSearch = '/v1/agent/search';

  static const agentCreate = '/v1/agent';
  static const brokerRegistration = '/v1/broker';
  static const uploadFile = '/v1/file';

  //FILTER
  static const brokerageFilterOptions = '/v1/broker';
  static const agentFilterOptions = '/v1/agent';
}
