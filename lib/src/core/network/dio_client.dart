import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:neorevv/src/data/repository/auth_data_repository.dart';
import '../../data/repository/get_access_token_repository_impl.dart';
import '../../domain/repository/get_access_token_repository.dart';
import '../services/flutter_secure_storage.dart';
import '/src/core/network/api_config.dart';
import 'dio_interceptor.dart';

class DioClient {
  DioClient._();

  static const String baseUrl = APIConfig.baseUrl;
  static final SessionManager _sessionManager = SessionManager();
  static final GetAccessTokenRepository _getAccessTokenRepository =
      GetAccessTokenRepositoryImpl();

  static Future<Dio> getDio() async {
    // final token = await _sessionManager.getToken();
    // final token =
    // "eyJhbGciOiJIUzUxMiJ9.************************************************************************************************************************************.o8mcyhs5yKoBmeqc6iWQ6b-1EaUC6CQwOzHxcAB4p1LfSSYei9V5agUs1OAMs8oLvS2DDoTf-UOtA2wNkEtwdg";
    final authRepository = AuthDataRepository();
    final token = authRepository.accessToken;

    final dio = Dio(
      BaseOptions(
        baseUrl: baseUrl,
        headers: {
          'accept': 'application/json',
          'Content-Type': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
        connectTimeout: const Duration(seconds: 10),
        receiveTimeout: const Duration(seconds: 10),
      ),
    );

    if (kIsWeb) {
      // Ensure cookies are included automatically on web
      dio.interceptors.add(
        InterceptorsWrapper(
          onRequest: (options, handler) {
            options.extra['withCredentials'] = true;
            handler.next(options);
          },
        ),
      );
    }

    dio.interceptors.add(DioInterceptor(_getAccessTokenRepository));
    return dio;
  }

  /// Get Dio instance for multipart form data requests
  static Future<Dio> getDioForMultipart() async {
    final token = await _sessionManager.getToken();
    final dio = Dio(
      BaseOptions(
        baseUrl: baseUrl,
        headers: {
          'accept': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
          // Don't set Content-Type here for multipart, let Dio handle it
        },
        connectTimeout: const Duration(
          seconds: 30,
        ), // Longer timeout for file uploads
        receiveTimeout: const Duration(seconds: 30),
      ),
    );

    dio.interceptors.add(DioInterceptor(_getAccessTokenRepository));
    return dio;
  }
}
